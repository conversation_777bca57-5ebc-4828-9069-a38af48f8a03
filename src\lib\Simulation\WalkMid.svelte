<script>
	import TwoSide from "$lib/Simulation/TwoSide.svelte";
	import WalkChoices from "./WalkChoices.svelte";
	import WalkSolution from "./WalkSolution.svelte";

    let {
        i,
        data,
        isMath,
        isSPR
    } = $props();
</script>
<!-- Middle -->
{#key isMath()}
<TwoSide isWalkThrough={true}>
    <!-- If the question is R&W -->
    {#if !isMath()}
        <div class="left">

            {#if data.practiceArena.questions[i].graph}
            <div class="graph">
                <img src="{data.practiceArena.questions[i].graph}" alt={data.practiceArena.questions[i].graph}>
            </div>
            {/if}

            {#if data.practiceArena.questions[i].intro}
                <div class="intro">
                    {@html data.practiceArena.questions[i].intro}
                </div>
            {/if}

            {#if data.practiceArena.questions[i].passage2}
                <div class="paired-text">Text 1</div>
            {/if}

            <!-- If the question is of type Student's notes -->
            {#if Array.isArray(data.practiceArena.questions[i].passage)}
                <ul class="qn-list fiction">
                    {#each data.practiceArena.questions[i].passage as point}
                        <li>{@html point}</li>            
                    {/each}
                </ul>
            {:else}
                <!-- Every other normal questions -->
                {#key i}
                <div class={`passage ${data.practiceArena.questions[i].intro ? 'fiction' : ''}`}>
                    {@html data.practiceArena.questions[i].passage}
                </div>
                {/key}
            {/if}

            {#if data.practiceArena.questions[i].passage2}
                <div class="paired-text text-2">Text 2</div>
            {/if}

            {#if data.practiceArena.questions[i].passage2}
                <div class="passage">{data.practiceArena.questions[i].passage2}</div>
            {/if}

            <div class="solution">
                <div class="solution-title">Solution:</div>
                
                <WalkSolution explanation={data.practiceArena.questions[i].explanation} />
            </div>
        

        </div>

        <WalkChoices {i} {data} {isMath} {isSPR} />
    
    <!-- If the question is MCQ math -->
    <!-- {:else if !isSPR()} -->

    <!-- If the question is SPR math -->
    {:else}
        <WalkChoices {i} {data} {isMath} {isSPR} isLeft={true}/>

        <div class="solution solution--right">
            <div class="solution-title">Solution:</div>
            
            <WalkSolution explanation={data.practiceArena.questions[i].explanation} />
        </div>
    {/if}
    
</TwoSide>
{/key}

<style>
    .left {
        margin: 32px 0 16px 64px;
        overflow-y: auto;
        scrollbar-color: #66E2FF#DAF8FF;
        scrollbar-width: thin;
    }

    .graph {
        margin-bottom: 16px;
        width: auto;
    }

    .graph img {
        object-fit: contain;
        width: 90%;
    }

    .intro {
        max-width: 645px;   
        width: 100%;
        color: var(--charcoal, #333);
        /* p1 Test */
        font-family: "Merriweather";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 25.1px; /* 156.875% */
    }

    .paired-text {
        color: #000;
        font-family: "Merriweather";
        font-size: 16px;
        font-style: normal;
        font-weight: 700;
        line-height: 25.1px; /* 156.875% */
        margin-bottom: 6px;
    }

    .text-2 {
        margin-top: 38px;
    }

    .qn-list {
        list-style-type: circle;
        margin-left: 1.8em;
        padding-left: 0;
        max-width: 645px;   
        color: var(--charcoal, #333);
        /* p1 Test */
        font-family: "Merriweather";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 25.1px; /* 156.875% */
    }

    .passage {
        max-width: 645px;   
        width: 100%;
        color: var(--charcoal, #333);
        /* p1 Test */
        font-family: "Merriweather";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 25.1px; /* 156.875% */
    }

    .fiction {
        padding: 10px 0 0 20px;
    }

    .solution {
        margin-top: 32px;
    }

    .solution--right {
        margin: 32px 32px 16px 32px;
        overflow-y: auto;
        scrollbar-color: #66E2FF#DAF8FF;
        scrollbar-width: thin;
    }

    .solution-title {
        font-size: 18px;
        font-weight: 700;
        font-family: "Merriweather";
    }

    .solution-passage {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }

    .solution-part {
        color: var(--Charcoal, #333);
        font-family: "Merriweather";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 25.1px; /* 156.875% */
    }

    /* width */
    ::-webkit-scrollbar {
        width: 8px;
        margin: 4px 0;
    }

    /* Track */
    ::-webkit-scrollbar-track {
        background: #DAF8FF;
        border-radius: 5px;
    }

    /* Handle */
    ::-webkit-scrollbar-thumb {
        background: #66E2FF;
        border-radius: 5px;
    }

    li {
        list-style-type: disc;
    }

</style>