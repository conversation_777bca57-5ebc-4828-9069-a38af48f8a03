<script>
    /**
     * @typedef {Object} Props
     * @property {boolean} [isSPR]
     * @property {boolean} [isWalkThrough]
     * @property {import('svelte').Snippet} [children]
     */

    /** @type {Props} */
    let { isSPR = false, isWalkThrough = false, children } = $props();
</script>

<div class="middle-container">
    <div class="middle" class:middle--small-gap={isSPR || isWalkThrough}>
        {@render children?.()}
        <div class="vr"></div>
    </div>
</div>

<style>
    .middle-container {
        display: flex;
        justify-content: center;
        height: calc(100vh - 180px);
    }

    .middle {
        position: relative;
        width: 100%;
        max-width: 1600px;
        display: inline-grid;
        grid-template-columns: 1fr 1fr;
        justify-content: space-between;
        gap: 64px;
        height: auto;
        /* margin: 50px 0; */
    }

    .middle--small-gap {
        gap : 16px;
    }

    .vr {
        width: 5px;
        height: calc(100% - 16px);
        background: #D9D9D9;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    /* width */
    ::-webkit-scrollbar {
        width: 8px;
        margin: 4px 0;
    }

    /* Track */
    ::-webkit-scrollbar-track {
        background: #DAF8FF;
        border-radius: 5px;
    }

    /* Handle */
    ::-webkit-scrollbar-thumb {
        background: #66E2FF;
        border-radius: 5px;
    }
</style>