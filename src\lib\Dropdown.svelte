<script>
    import Section from "./Section.svelte";

    let {
        label,
        isRequired,
        dropdownText = $bindable(),
        dropdownChoices
    } = $props();

    let isDropdownOpen = $state(false);

    function changeDropdown() {
        isDropdownOpen = !isDropdownOpen;
    }

    function changeDropdownText(choice) {
        dropdownText = choice;
    }
</script>

<Section>
    <p class="p2-desktop">
        {label}:
        {#if isRequired}
            <span class="red-asterisk">*</span>
        {/if}
    </p>
    <button class="form-dropdown" onclick={changeDropdown}>
        <p class="p2-desktop">{dropdownText ?? "Select"}</p>
        <div class="dropdown-icon">
            {#if !isDropdownOpen}
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12.71 16.04L18.36 10.38C18.4537 10.287 18.5281 10.1764 18.5789 10.0546C18.6297 9.9327 18.6558 9.80199 18.6558 9.66998C18.6558 9.53797 18.6297 9.40726 18.5789 9.28541C18.5281 9.16355 18.4537 9.05294 18.36 8.95998C18.1726 8.77373 17.9192 8.66919 17.655 8.66919C17.3908 8.66919 17.1374 8.77373 16.95 8.95998L11.95 13.91L6.99999 8.95998C6.81263 8.77373 6.55918 8.66919 6.29499 8.66919C6.03081 8.66919 5.77736 8.77373 5.58999 8.95998C5.49551 9.0526 5.42034 9.16304 5.36884 9.28492C5.31734 9.40679 5.29054 9.53767 5.29 9.66998C5.29054 9.80229 5.31734 9.93317 5.36884 10.055C5.42034 10.1769 5.49551 10.2874 5.58999 10.38L11.24 16.04C11.3336 16.1415 11.4473 16.2225 11.5738 16.2779C11.7003 16.3333 11.8369 16.3619 11.975 16.3619C12.1131 16.3619 12.2497 16.3333 12.3762 16.2779C12.5027 16.2225 12.6164 16.1415 12.71 16.04Z" fill="black"/>
                </svg>
            {:else}
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M11.29 8.45977L5.64004 14.1198C5.54631 14.2127 5.47191 14.3233 5.42115 14.4452C5.37038 14.5671 5.34424 14.6978 5.34424 14.8298C5.34424 14.9618 5.37038 15.0925 5.42115 15.2144C5.47191 15.3362 5.54631 15.4468 5.64004 15.5398C5.8274 15.726 6.08085 15.8306 6.34504 15.8306C6.60922 15.8306 6.86267 15.726 7.05004 15.5398L12.05 10.5898L17 15.5398C17.1874 15.726 17.4409 15.8306 17.705 15.8306C17.9692 15.8306 18.2227 15.726 18.41 15.5398C18.5045 15.4472 18.5797 15.3367 18.6312 15.2148C18.6827 15.093 18.7095 14.9621 18.71 14.8298C18.7095 14.6975 18.6827 14.5666 18.6312 14.4447C18.5797 14.3228 18.5045 14.2124 18.41 14.1198L12.76 8.45977C12.6664 8.35827 12.5527 8.27726 12.4262 8.22185C12.2997 8.16645 12.1631 8.13784 12.025 8.13784C11.8869 8.13784 11.7503 8.16645 11.6238 8.22185C11.4973 8.27726 11.3837 8.35827 11.29 8.45977Z" fill="black"/>
                </svg> 
            {/if}    
        </div>
        {#if isDropdownOpen}
        <div class="dropdown-content">
            {#each dropdownChoices as choice}
                <button class="dropdown-choice-container" class:dropdown-choice-chosen={choice === dropdownText} onclick={() => changeDropdownText(choice)}>
                    <p class="p2-desktop">{choice}</p>
                </button>
            {/each}
        </div>
        {/if}

    </button>
</Section>

<style>
    .red-asterisk {
        color: #FF0000;
    }

    .form-dropdown {
        position: relative;
        padding: 6px 14px;
        display: inline-flex;
        gap: 10px;
        align-items: center;
        border: 1px solid #CED7DA;
        border-radius: 4px;
        width: fit-content;
    }

    .dropdown-icon {
        display: flex;
        align-items: center;
    }

    .dropdown-content {
        position: absolute;
        bottom: 0;
        left: 0;
        transform: translate(0, 100%);
        background-color: white;
        border: 1px solid #CED7DA;
        border-radius: 4px;
        z-index: 2;
    }

    .dropdown-choice-container {
        display: flex;
        align-items: center;
        padding: 6px 14px;
        width: 350px;
    }

    .dropdown-choice-container:hover, .dropdown-choice-chosen{
        background-color: #DAF8FF;
    }
</style>