<script>
    let { explanation } = $props();
</script>

{#key explanation}
<div class="solution-passage">
        
    {@html explanation}

</div>
{/key}

<style>
    .solution-passage {
        display: flex;
        flex-direction: column;
        gap: 8px;
        color: var(--<PERSON><PERSON><PERSON><PERSON>, #333);
        font-family: "Merriweather";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 25.1px; /* 156.875% */
    }
</style>