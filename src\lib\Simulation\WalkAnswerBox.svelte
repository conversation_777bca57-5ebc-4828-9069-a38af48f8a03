<script>
    let { correctAnswer } = $props();
</script>

<!-- Answer box -->
<div class="answer-box-container">
    <div class="answer-box answer-box--correct">
        <span class="answer-input" type="text">
            {""}
        </span>
    </div>
</div>

<!-- Answer Preview -->
<div class="correct-answer-container">
        <div class="correct-answer">
            Correct Answer: ${correctAnswer.recommended.join("$ or $")}$
        </div>                    
</div>


<style>
    
    .answer-box-container {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        padding-top: 20px;
        width: 100%;
    }

    .answer-box--correct {
        background: var(--Web-color-Light-Aquamarine, #D1FFEE);
    }

    .answer-box {
        width: 105px;
        height: 57px;
        border-radius: 8px;
        border: 1px solid #333333;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .answer-input {
        width: 90px;
        bottom: 8px;
        border: 0;
        outline: 0;
        background: transparent;
        border-bottom: 1px solid #333333;
        position: absolute;
        color: #505050;
        font-family: "Chivo";
        font-size: 28px;
        font-style: normal;
        font-weight: 400;
        line-height: 25.1px; /* 89.643% */
    }

    .correct-answer-container {
        display: flex;
        padding-top: 40px;
        align-items: center;
        justify-content: flex-start;
        gap: 12px;
        width: 100%;
    }

    .correct-answer {
        color: #000;
        font-family: "Merriweather";
        font-size: 20px;
        font-style: normal;
        font-weight: 700;
        line-height: 25.1px; /* 125.5% */
    }
</style>