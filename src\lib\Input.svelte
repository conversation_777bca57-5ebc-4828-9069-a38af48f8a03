<script>
    import Section from "./Section.svelte";


    let {
        label,
        placeholder = label,
        isRequired,
        inputValue = $bindable()
    } = $props();
</script>
<Section>
    <p class="p2-desktop">
        {label}:
        {#if isRequired}
            <span class="red-asterisk">*</span>
        {/if}
    </p>
    <input class="form-input p2-desktop" placeholder="Enter {placeholder}" bind:value={inputValue} />
</Section>

<style>
    .form-input {
        padding: 6px 14px;
        display: inline-flex;
        gap: 10px;
        align-items: center;
        border: 1px solid #CED7DA;
        border-radius: 4px;
        width: 100%;
    }

    .red-asterisk {
        color: #FF0000;
    }
</style>