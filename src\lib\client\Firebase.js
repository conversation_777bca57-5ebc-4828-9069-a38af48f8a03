 // Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
// import { getAnalytics } from "firebase/analytics";
import { getAuth, GoogleAuthProvider, FacebookAuthProvider, onAuthStateChanged } from "firebase/auth";
import { getFirestore } from "firebase/firestore"
import { writable } from "svelte/store";

// TODO: Add SDKs for Firebase products that you want to use
// https://firebase.google.com/docs/web/setup#available-libraries

// Your web app's Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional

// Initialize Firebase
export const app = initializeApp({
  apiKey: "AIzaSyDTY-WDWMupCgzvT3VzFfgR_w6a1KI1EyI",
  authDomain: "dsat16-17663.firebaseapp.com",
  projectId: "dsat16-17663",
  storageBucket: "dsat16-17663.appspot.com",
  messagingSenderId: "347596663150",
  appId: "1:347596663150:web:04100f0bdfea1ae856797f",
  measurementId: "G-2QZCQB6S2V"
});
// const analytics = getAnalytics(app);

// Initialize Firebase Authentication and get a reference to the service
export const auth = getAuth(app);
export const googleProvider = new GoogleAuthProvider();
export const facebookProvider = new FacebookAuthProvider();

// DB
export const db = getFirestore(app);

function userStore() {
  let unsubscribe;

  if (!auth || !globalThis.window) {
    // console.warn('Auth is not initialized or not in browser');
    const { subscribe } = writable(null);
    return {
      subscribe,
    }
  }

  const { subscribe } = writable(auth?.currentUser ?? null, (set) => {
    unsubscribe = onAuthStateChanged(auth, (user) => {
      set(user);
    });

    return () => unsubscribe();
  });

  return {
    subscribe,
  };
}

export const user = userStore();