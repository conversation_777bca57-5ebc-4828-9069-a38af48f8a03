rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
  	
    match /{documents=**} {
    	allow delete: if false;
    }
    
    // Allow only authenticated content owners access
    match /users/{userId} {
      allow read, create, update: if isValidUser(userId);
    }
    
    // PraticeArena collection
    match /PracticeArena/{arena} {
      // Access to a specific document is granted if the user is authenticated and the user's id matches the 'user' field of the document
      allow read: if is<PERSON>alid<PERSON>ser(resource.data.user);
      allow create: if isValidUser(request.resource.data.user);
      allow update: if false;
    }
    
    function isValidUser(userId) {
    	let isSignedIn = request.auth != null;
      let isOwner = request.auth.uid == userId;
      return isSignedIn && isOwner;
    }
  }
}