<script>
    import { auth, googleProvider, db } from "$lib/client/Firebase.js";
    import { signInWithPopup } from "firebase/auth";
	import { doc, setDoc } from "firebase/firestore";
    import { page } from "$app/state";

    async function signIn(provider) {
        try {
            const credential = await signInWithPopup(auth, provider);

            const idToken = await credential.user.getIdToken();

            const res = await fetch("/api/sign-in", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    // 'CSRF-Token': csrfToken  // HANDLED by sveltekit automatically
                },
                body: JSON.stringify({ idToken }),
            });            

            // * If user signed in for the first time
            if (credential.user.metadata.creationTime === credential.user.metadata.lastSignInTime) {
                const userRef = doc(db, 'users', credential.user.uid);
                await setDoc(userRef, {
                    gmail: credential.user.email,
                    paid: false,
                    isAdmin: false,
                }, { merge: true })
            }

            // TODO: redirect to console as default
            let redirectTo = page.url.searchParams.get("redirectTo") ?? "/jsonify"; 

            window.location.href = redirectTo;
        } catch (error) {
            console.log(error);
        }
    }
</script>

<div class="webpage">
    <div class="logo">
        <svg xmlns="http://www.w3.org/2000/svg" width="149" height="30" viewBox="0 0 149 30" fill="none">
            <path d="M118.603 0.39924V29.6008H111.559V6.98669H111.388L104.857 10.9791V4.87643L112.058 0.39924H118.603Z" fill="url(#paint0_linear_541_389)"/>
            <path d="M136.764 30C135.167 30 133.637 29.7433 132.173 29.23C130.709 28.7072 129.407 27.8802 128.266 26.749C127.126 25.6084 126.227 24.116 125.571 22.2719C124.916 20.4183 124.592 18.1606 124.602 15.499C124.611 13.0941 124.906 10.9363 125.486 9.02567C126.066 7.10551 126.893 5.47529 127.967 4.13498C129.05 2.79468 130.343 1.77281 131.845 1.06939C133.357 0.356464 135.044 0 136.907 0C138.951 0 140.752 0.39924 142.311 1.19772C143.879 1.98669 145.134 3.05133 146.075 4.39163C147.016 5.72243 147.572 7.20532 147.743 8.8403H140.8C140.59 7.91825 140.129 7.21958 139.416 6.7443C138.713 6.2595 137.877 6.01711 136.907 6.01711C135.12 6.01711 133.784 6.79182 132.9 8.34125C132.026 9.89068 131.579 11.9629 131.56 14.558H131.745C132.145 13.6835 132.72 12.9325 133.471 12.3051C134.222 11.6778 135.082 11.1977 136.051 10.865C137.031 10.5228 138.067 10.3517 139.16 10.3517C140.909 10.3517 142.454 10.7557 143.794 11.5637C145.134 12.3717 146.185 13.4791 146.945 14.8859C147.705 16.2833 148.081 17.885 148.071 19.6911C148.081 21.7253 147.606 23.5219 146.646 25.0808C145.685 26.6302 144.355 27.8375 142.653 28.7025C140.961 29.5675 138.998 30 136.764 30ZM136.722 24.5817C137.587 24.5817 138.361 24.3774 139.046 23.9686C139.73 23.5599 140.267 23.0038 140.657 22.3004C141.047 21.597 141.237 20.8032 141.227 19.9192C141.237 19.0257 141.047 18.2319 140.657 17.538C140.277 16.8441 139.744 16.2928 139.06 15.884C138.385 15.4753 137.61 15.2709 136.736 15.2709C136.099 15.2709 135.505 15.3897 134.954 15.6274C134.402 15.865 133.922 16.1977 133.513 16.6255C133.114 17.0437 132.8 17.538 132.572 18.1084C132.344 18.6692 132.225 19.2776 132.216 19.9335C132.225 20.7985 132.425 21.5827 132.815 22.2861C133.204 22.9895 133.737 23.5504 134.412 23.9686C135.087 24.3774 135.857 24.5817 136.722 24.5817Z" fill="url(#paint1_linear_541_389)"/>
            <path d="M10.344 28.9314H0V0.94657H10.3303C13.1816 0.94657 15.6367 1.50681 17.6955 2.6273C19.7633 3.73867 21.3575 5.34197 22.478 7.43718C23.5985 9.52329 24.1587 12.0193 24.1587 14.9253C24.1587 17.8404 23.5985 20.3455 22.478 22.4407C21.3666 24.536 19.777 26.1438 17.7091 27.2643C15.6412 28.3757 13.1862 28.9314 10.344 28.9314ZM6.7639 23.165H10.0844C11.6512 23.165 12.9767 22.9008 14.0607 22.3724C15.1539 21.835 15.9783 20.965 16.534 19.7625C17.0988 18.5509 17.3812 16.9385 17.3812 14.9253C17.3812 12.9121 17.0988 11.3088 16.534 10.1154C15.9692 8.91294 15.1357 8.04753 14.0334 7.51917C12.9402 6.9817 11.592 6.71297 9.98871 6.71297H6.7639V23.165Z" fill="#303030"/>
            <path d="M43.2343 9.33654C43.1432 8.33448 42.7378 7.55561 42.0182 6.99992C41.3076 6.43512 40.2919 6.15272 38.971 6.15272C38.0965 6.15272 37.3677 6.2666 36.7847 6.49434C36.2017 6.72208 35.7644 7.03636 35.4729 7.43718C35.1814 7.8289 35.0311 8.27982 35.022 8.78996C35.0038 9.20901 35.0857 9.57795 35.2679 9.89678C35.4592 10.2156 35.7325 10.498 36.0878 10.744C36.4522 10.9808 36.8895 11.1904 37.3996 11.3725C37.9097 11.5547 38.4836 11.7142 39.1213 11.8508L41.5263 12.3974C42.9109 12.698 44.1316 13.0988 45.1883 13.5998C46.2542 14.1009 47.1469 14.6976 47.8666 15.3899C48.5953 16.0822 49.1465 16.8793 49.52 17.7812C49.8935 18.683 50.0848 19.6942 50.0939 20.8147C50.0848 22.5819 49.6384 24.0987 48.7548 25.3649C47.8711 26.6312 46.6003 27.6014 44.9424 28.2755C43.2935 28.9496 41.3031 29.2866 38.971 29.2866C36.6298 29.2866 34.5893 28.9359 32.8493 28.2345C31.1094 27.533 29.7566 26.4672 28.791 25.037C27.8254 23.6068 27.3289 21.7985 27.3016 19.6122H33.7785C33.8332 20.5141 34.0746 21.2656 34.5027 21.8668C34.9309 22.4681 35.5185 22.9236 36.2654 23.2333C37.0215 23.543 37.8961 23.6979 38.889 23.6979C39.8 23.6979 40.5743 23.5749 41.212 23.3289C41.8588 23.083 42.3552 22.7414 42.7014 22.3041C43.0476 21.8668 43.2252 21.3658 43.2343 20.801C43.2252 20.2727 43.0612 19.8217 42.7424 19.4482C42.4236 19.0656 41.9316 18.7377 41.2666 18.4644C40.6107 18.182 39.7727 17.9224 38.7524 17.6855L35.8282 17.0023C33.405 16.4466 31.4966 15.5493 30.1028 14.3104C28.709 13.0624 28.0167 11.3771 28.0258 9.25456C28.0167 7.52372 28.4813 6.00697 29.4196 4.70429C30.3578 3.40161 31.656 2.38589 33.3139 1.65712C34.9719 0.928351 36.8621 0.563965 38.9847 0.563965C41.1528 0.563965 43.0339 0.932905 44.6281 1.67078C46.2314 2.39956 47.4749 3.42439 48.3585 4.74529C49.2421 6.06618 49.693 7.5966 49.7113 9.33654H43.2343Z" fill="#303030"/>
            <path d="M58.6991 28.9314H51.4296L60.8717 0.94657H69.8766L79.3187 28.9314H72.0492L65.4766 7.99743H65.258L58.6991 28.9314ZM57.7289 17.9178H72.9237V23.0556H57.7289V17.9178Z" fill="#303030"/>
            <path d="M77.9215 6.43968V0.94657H101.575V6.43968H93.0891V28.9314H86.4208V6.43968H77.9215Z" fill="#303030"/>
            <defs>
              <linearGradient id="paint0_linear_541_389" x1="150.125" y1="-5.86693" x2="116.834" y2="-10.7627" gradientUnits="userSpaceOnUse">
                <stop stop-color="#66E2FF"/>
                <stop offset="1" stop-color="#FF66C4"/>
              </linearGradient>
              <linearGradient id="paint1_linear_541_389" x1="150.125" y1="-5.86693" x2="116.834" y2="-10.7627" gradientUnits="userSpaceOnUse">
                <stop stop-color="#66E2FF"/>
                <stop offset="1" stop-color="#FF66C4"/>
              </linearGradient>
            </defs>
        </svg>
    </div>
    <div class="signup-box">
        <div class="signup-text">Admin</div>
        <div class="button-container">
            <!-- <button class="fb-signup" on:click={async () => await signIn(facebookProvider)}>
                <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30" fill="none">
                    <path d="M26.125 2.5H3.875C3.51033 2.5 3.16059 2.64487 2.90273 2.90273C2.64487 3.16059 2.5 3.51033 2.5 3.875V26.125C2.5 26.3056 2.53557 26.4844 2.60467 26.6512C2.67377 26.818 2.77505 26.9696 2.90273 27.0973C3.03041 27.225 3.18199 27.3262 3.34881 27.3953C3.51563 27.4644 3.69443 27.5 3.875 27.5H15.85V17.8125H12.6V14.0625H15.85V11.25C15.7827 10.5897 15.8606 9.92266 16.0784 9.29567C16.2962 8.66868 16.6485 8.09693 17.1106 7.62051C17.5727 7.1441 18.1335 6.77455 18.7535 6.5378C19.3736 6.30104 20.038 6.20281 20.7 6.25C21.6729 6.24401 22.6454 6.2941 23.6125 6.4V9.775H21.625C20.05 9.775 19.75 10.525 19.75 11.6125V14.025H23.5L23.0125 17.775H19.75V27.5H26.125C26.3056 27.5 26.4844 27.4644 26.6512 27.3953C26.818 27.3262 26.9696 27.225 27.0973 27.0973C27.225 26.9696 27.3262 26.818 27.3953 26.6512C27.4644 26.4844 27.5 26.3056 27.5 26.125V3.875C27.5 3.69443 27.4644 3.51563 27.3953 3.34881C27.3262 3.18199 27.225 3.03041 27.0973 2.90273C26.9696 2.77505 26.818 2.67377 26.6512 2.60467C26.4844 2.53557 26.3056 2.5 26.125 2.5Z" fill="black"/>
                </svg>
                <div class="but-text">Continue with Facebook</div>
            </button> -->
            <button class="gg-signup" onclick={async () => await signIn(googleProvider)}>
                <svg xmlns="http://www.w3.org/2000/svg" width="30" height="31" viewBox="0 0 30 31" fill="none">
                    <path d="M28.2529 13.3771C28.2008 13.0889 28.0492 12.828 27.8244 12.6402C27.5996 12.4523 27.316 12.3494 27.023 12.3493H15.25C15.0859 12.3493 14.9233 12.3816 14.7716 12.4444C14.6199 12.5072 14.4821 12.5992 14.366 12.7153C14.25 12.8314 14.1579 12.9692 14.0951 13.1209C14.0323 13.2726 14 13.4351 14 13.5993V18.4345C14 18.5987 14.0323 18.7612 14.0951 18.9129C14.1579 19.0646 14.25 19.2024 14.366 19.3185C14.4821 19.4346 14.6199 19.5266 14.7716 19.5894C14.9233 19.6522 15.0859 19.6845 15.25 19.6845H20.203C19.8501 20.2802 19.3663 20.7878 18.7882 21.1689C17.7318 21.8387 16.5005 22.1802 15.25 22.1503C13.9531 22.1355 12.6939 21.7123 11.6513 20.9408C10.6086 20.1694 9.83567 19.0889 9.44223 17.8531L9.44186 17.851C9.00403 16.5677 9.00403 15.1755 9.44186 13.8922L9.44216 13.8902C9.83589 12.6546 10.609 11.5744 11.6516 10.8032C12.6942 10.032 13.9533 9.60898 15.25 9.59417C15.9705 9.57764 16.6872 9.7036 17.3589 9.96482C18.0306 10.226 18.6441 10.6174 19.1642 11.1164C19.4003 11.3421 19.7155 11.4664 20.0422 11.4628C20.3688 11.4591 20.6811 11.3278 20.9122 11.0969L24.4974 7.51165C24.6162 7.39303 24.7098 7.25168 24.7728 7.09605C24.8357 6.94042 24.8666 6.7737 24.8636 6.60586C24.8606 6.43802 24.8239 6.2725 24.7555 6.11918C24.6871 5.96587 24.5886 5.82792 24.4657 5.71356C21.9708 3.37444 18.6697 2.08801 15.25 2.12225C12.6999 2.1147 10.1985 2.81976 8.02769 4.15792C5.8569 5.49608 4.10315 7.41411 2.9642 9.69572L2.96236 9.69794C1.99677 11.6126 1.4958 13.7279 1.50003 15.8722C1.50307 18.0158 2.00367 20.1294 2.96243 22.0466L2.96426 22.0488C4.10321 24.3304 5.85695 26.2484 8.02772 27.5866C10.1985 28.9247 12.7 29.6298 15.25 29.6222C18.6067 29.7072 21.8689 28.5056 24.3684 26.2635L24.3693 26.2629L24.3703 26.2618L24.3705 26.2617L24.3706 26.2615C25.7254 24.9582 26.7926 23.3859 27.5038 21.6457C28.215 19.9055 28.5544 18.0358 28.5002 16.1567C28.5007 15.2247 28.4179 14.2944 28.2529 13.3771ZM15.25 4.62225C17.5889 4.59948 19.8725 5.33293 21.7607 6.71331L19.9436 8.52975C18.5658 7.57607 16.9256 7.07438 15.25 7.09417C13.7203 7.10204 12.2203 7.51741 10.9044 8.29753C9.58857 9.07765 8.50435 10.1943 7.76339 11.5326L6.42688 10.4967L5.69563 9.92964C6.70159 8.30194 8.10811 6.95938 9.78083 6.0302C11.4535 5.10102 13.3366 4.61627 15.25 4.62225ZM4.59933 19.5014C3.80018 17.148 3.80018 14.5965 4.59933 12.2431L6.82711 13.9704C6.54228 15.2219 6.54228 16.5214 6.82711 17.7729L4.59933 19.5014ZM15.25 27.1222C13.3365 27.1282 11.4535 26.6434 9.78075 25.7142C8.10802 24.785 6.70149 23.4424 5.69553 21.8146L6.16854 21.4477L7.76339 20.2106C8.50402 21.5493 9.58813 22.6664 10.9041 23.4467C12.22 24.2271 13.7201 24.6426 15.25 24.6503C16.6821 24.6672 18.0979 24.3456 19.3822 23.7118L21.4968 25.3535C19.637 26.5518 17.4619 27.1676 15.25 27.1222ZM23.4074 23.6713L23.1786 23.4938L21.4569 22.1572C22.3597 21.1819 22.9665 19.97 23.2065 18.6628C23.2401 18.4824 23.2335 18.2968 23.1872 18.1192C23.1409 17.9416 23.0561 17.7764 22.9388 17.6353C22.8214 17.4941 22.6744 17.3806 22.5083 17.3027C22.3421 17.2248 22.1608 17.1845 21.9773 17.1846H16.5V14.8493H25.9361C25.9788 15.2814 26.0002 15.7184 26.0002 16.1567C26.0732 18.8927 25.1521 21.5625 23.4073 23.6713H23.4074Z" fill="black"/>
                </svg>
                <div class="but-text">Continue with Google</div>
            </button>
        </div>
    </div>
</div>

<style>
    .webpage {
        background-color: #DAF8FF;
        min-height: 100svh;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .logo {
        margin-bottom: 20px;
    }

    .signup-box {
        display: inline-flex;
        padding: 50px 40px;
        flex-direction: column;
        align-items: flex-start;
        gap: 45px;
        border-radius: 8px;
        border: 1px solid #000;
        background: #FFF;
    }

    .signup-text {
        color: #000;
        font-family: 'Inter';
        font-size: 58px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
    }

    .gg-signup {
        display: flex;
        width: 400px;
        height: 50px;
        padding: 10px;
        justify-content: center;
        align-items: center;
        gap: 10px;
        flex-shrink: 0;
        border-radius: 8px;
        border: 1px solid #000;
        background: #66E2FF;
        box-shadow: 4px 4px 0px 0px #000;
    }

    .gg-signup {
        background-color: white;
        margin-top: 10px;
    }

    .but-text {
        color: #000;
        font-family: 'Open Sans';
        font-size: 18px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
    }

    button:active {
        transform: translate(4px, 4px);
        box-shadow: none;
    }

    @media (max-width: 767px) {

        .signup-text {
            font-size: 36px;
        }

        .signup-box {
            padding: 50px 30px;
        }

        .gg-signup {
            width: 280px;
        }
    }
    
</style>