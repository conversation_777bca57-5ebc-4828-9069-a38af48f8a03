<script>
   let { data } = $props();
</script>
   
<!-- top -->
<div class="top">
    <div class="heading">
        <div class="title">{data.practiceArena.title}</div>
        <button class="directions">
            <div class="directions-text">Directions</div>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M12.71 15.5397L18.36 9.87974C18.4537 9.78677 18.5281 9.67617 18.5789 9.55431C18.6296 9.43246 18.6558 9.30175 18.6558 9.16974C18.6558 9.03773 18.6296 8.90702 18.5789 8.78516C18.5281 8.6633 18.4537 8.5527 18.36 8.45974C18.1726 8.27349 17.9191 8.16895 17.655 8.16895C17.3908 8.16895 17.1373 8.27349 16.95 8.45974L11.95 13.4097L6.99996 8.45974C6.8126 8.27349 6.55915 8.16895 6.29496 8.16895C6.03078 8.16895 5.77733 8.27349 5.58996 8.45974C5.49548 8.55235 5.42031 8.6628 5.36881 8.78467C5.31731 8.90655 5.29051 9.03743 5.28996 9.16974C5.29051 9.30204 5.31731 9.43293 5.36881 9.5548C5.42031 9.67668 5.49548 9.78712 5.58996 9.87974L11.24 15.5397C11.3336 15.6412 11.4473 15.7223 11.5738 15.7777C11.7003 15.8331 11.8369 15.8617 11.975 15.8617C12.1131 15.8617 12.2497 15.8331 12.3762 15.7777C12.5027 15.7223 12.6163 15.6412 12.71 15.5397Z" fill="#B2B2B2"/>
            </svg>
        </button>
    </div>
    <div class="clock">
        <div class="timer">00:00</div>
        <button class="hide-timer">
            <div class="hide-timer-text">Hide</div>
        </button>
    </div>
    <div class="tools">
        <button class="more">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M12 7C12.3956 7 12.7822 6.8827 13.1111 6.66294C13.44 6.44318 13.6964 6.13082 13.8478 5.76537C13.9991 5.39992 14.0387 4.99778 13.9616 4.60982C13.8844 4.22186 13.6939 3.86549 13.4142 3.58579C13.1345 3.30608 12.7781 3.1156 12.3902 3.03843C12.0022 2.96126 11.6001 3.00087 11.2346 3.15224C10.8692 3.30362 10.5568 3.55996 10.3371 3.88886C10.1173 4.21776 10 4.60444 10 5C10 5.53043 10.2107 6.03914 10.5858 6.41421C10.9609 6.78929 11.4696 7 12 7ZM12 17C11.6044 17 11.2178 17.1173 10.8889 17.3371C10.56 17.5568 10.3036 17.8692 10.1522 18.2346C10.0009 18.6001 9.96126 19.0022 10.0384 19.3902C10.1156 19.7781 10.3061 20.1345 10.5858 20.4142C10.8655 20.6939 11.2219 20.8844 11.6098 20.9616C11.9978 21.0387 12.3999 20.9991 12.7654 20.8478C13.1308 20.6964 13.4432 20.44 13.6629 20.1111C13.8827 19.7822 14 19.3956 14 19C14 18.4696 13.7893 17.9609 13.4142 17.5858C13.0391 17.2107 12.5304 17 12 17ZM12 10C11.6044 10 11.2178 10.1173 10.8889 10.3371C10.56 10.5568 10.3036 10.8692 10.1522 11.2346C10.0009 11.6001 9.96126 12.0022 10.0384 12.3902C10.1156 12.7781 10.3061 13.1345 10.5858 13.4142C10.8655 13.6939 11.2219 13.8844 11.6098 13.9616C11.9978 14.0387 12.3999 13.9991 12.7654 13.8478C13.1308 13.6964 13.4432 13.44 13.6629 13.1111C13.8827 12.7822 14 12.3956 14 12C14 11.4696 13.7893 10.9609 13.4142 10.5858C13.0391 10.2107 12.5304 10 12 10Z" fill="#B2B2B2"/>
            </svg>
            <div class="more-text">More</div>
        </button>
    </div>
</div>


<style>
    button {
        border: 0;
        background: none;
    }

    button:hover {
        opacity: 1 !important;
        cursor: auto !important;
    }

    .top {
        width: 100%;
        height: 90px;
        display: inline-flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 64px;
        border-bottom: 3px solid #505050;
        display: flex;
        position: relative;
    }

    .heading {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .title {
        color: #000;
        font-family: "Open Sans";
        font-size: 20px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
    }

    .directions {
        display: flex;
        align-items: center;
        margin-right: auto;
    }

    .directions-text {
        color: #B2B2B2;
        font-family: "Inter";
        font-size: 15px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
    }

    .clock {
        display: flex;
        flex-direction: column;
        gap: 12px;
        position: absolute;
        left: 50%;
        transform: translate(-50%, 0);
    }

    .timer {
        color: #B2B2B2;
        text-align: center;
        font-family: "Inter";
        font-size: 20px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
    }
    
    .hide-timer {
        display: inline-flex;
        padding: 3px 13px;
        justify-content: center;
        align-items: center;
        border-radius: 18px;
        border: 1px solid #B2B2B2;
    }

    .hide-timer-text {
        color: #B2B2B2;
        font-family: "Inter";
        font-size: 13px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
    }

    .tools {
        display: inline-flex;
        align-items: flex-start;
        gap: 27px;
        width: 191.7px;
        justify-content: end;
    }

    .more {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .more-text {
        color: #B2B2B2;
        font-family: "Inter";
        font-size: 12px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
    }
</style>