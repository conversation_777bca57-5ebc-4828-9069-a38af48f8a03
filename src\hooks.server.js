import { adminAuth, adminDB } from "$lib/server/admin.js";
import { redirect, error } from "@sveltejs/kit";

export async function handle({ event, resolve }) {
  const fromUrl = "/" + (event.url.pathname + event.url.search).slice(1);

  const sessionCookie = event.cookies.get("__session");

  const excludedPaths = ["/sign-up", "/api/sign-in"];

  if (!excludedPaths.includes(event.url.pathname)) {
      // If there's no session cookie, redirect to sign-up page
      if (!sessionCookie) {
        redirect(302, `/sign-up?redirectTo=${fromUrl}`);
      }

      const decodedClaims = await adminAuth.verifySessionCookie(sessionCookie);
      event.locals.uid = decodedClaims.uid;

      // If the user is not signed in, redirect to sign-up page
      if (!event.locals.uid) {
        redirect(302, `/sign-up?redirectTo=${fromUrl}`);
      }

      // If the user is not admin, deny access
      let docSnap = await adminDB.collection("users").doc(event.locals.uid).get();
      let isAdmin = await docSnap.get('isAdmin');

      if (!isAdmin) {
        error(403, "You are not authorized to access this page");
      }
  }

  return resolve(event);
}