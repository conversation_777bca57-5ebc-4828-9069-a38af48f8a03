<script>
    /**
     * @typedef {Object} Props
     * @property {import('svelte').Snippet} [children]
     */

    /** @type {Props} */
    let { children } = $props();
</script>

<div class="wrapper">
    {@render children?.()}
</div>

<style>
    .wrapper {
        display: flex;
        flex-direction: column;
        gap: 1.25em;
        
        font-family: "Open Sans";
        font-size: 1.25em;
        font-weight: 450;
        line-height: 1.875em;
    }
</style>