<script>
    /**
     * @typedef {Object} Props
     * @property {import('svelte').Snippet} [children]
     */

    /** @type {Props} */
    let { children } = $props();
</script>

<h1>
    {@render children?.()}
</h1>

<style>
    h1 {
        font-family: "Inter";
        font-size: 3.625em;
        font-weight: 600;
    }

    /* @media (max-width: 540px) {
        h1 {
            font-size: 2.25em;
        }
    } */
</style>
