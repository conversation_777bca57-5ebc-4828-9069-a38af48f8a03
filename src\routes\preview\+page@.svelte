<script>
	import WalkthroughMiddle from "$lib/Simulation/WalkMid.svelte";
	import WalkthroughTop from "$lib/Simulation/WalkTop.svelte";
	import WalkthroughBottom from "$lib/Simulation/WalkBottom.svelte";
	import WalkthroughReview from "$lib/Simulation/WalkReview.svelte";
    
    let { data } = $props();

    function isMath() {
        return ["Algebra", "Data Analysis", "Geometry"].includes(data.practiceArena.questions[i].questionType);
    }

    let i = $state(data.type === 'question' ? 0 : data.practiceArena.questions.length);

    function nextQuestion() {
        i++;
    }

    function previousQuestion() {
        if (i === 0) return;
        i--;
    }

    function setQuestion(question) {
        i = question;
    }

    function toReview() {
        i = data.practiceArena.questions.length;
    }

    let isSPR = $derived(!data.practiceArena.questions[i]?.choices ?? false);
</script>

<div class="hide">
    <WalkthroughTop {data} />

    {#if i > data.practiceArena.questions.length - 1}
        <WalkthroughReview {data} {setQuestion} />
    {:else}
        <WalkthroughMiddle {data} {i} {isMath} {isSPR}/>
    {/if}
    
    <WalkthroughBottom {data} {i} {nextQuestion} {setQuestion} {previousQuestion} {toReview} />
</div>

<div class="show-wrapper">
    <div class="show">
        <div class="title">{data.practiceArena.title}</div>
        <div class="caution">Lưu ý</div>
        <div class="text">
            Tính năng không được hỗ trợ trên thiết bị này. Để truy cập {data.practiceArena.title}, vui lòng chuyển sang thiết bị có màn hình lớn hơn.
        </div>
    </div>
</div>

<style>
    .show {
        display: none;
    }

    @media only screen and (max-width: 960px) {
        .hide {
            display: none;
        }

        .show-wrapper {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100vw;
            height: 100vh;
        }

        .show {
            width: 75%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            gap: 14px;
            margin: 64px;
        }

        .title {
            color: #000;
            font-family: "Inter";
            font-size: 30px;
            font-style: normal;
            font-weight: 600;
            line-height: normal;
        }

        .caution {
            color: #000;
            font-family: "Inter";
            font-size: 24px;
            font-style: normal;
            font-weight: 600;
            line-height: normal;
        }

        .text {
            color: #000;
            font-family: "Open Sans";
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px;
            text-align: center;
        }
    }
</style>