<script>
    import Dropdown from "$lib/Dropdown.svelte";
    import Button from "$lib/Button.svelte";
    import H4 from "$lib/Typography/H4.svelte";

    const questionTypes = ["Transitions (W1)", "Student's Notes (W1)", "Punctuations (W2)", "Grammar (W3)", "Word in Context (R1)", "Main Idea (R2)", "Specific Detail (R2)", "Main Purpose (R2)", "Overall Structure (R2)", "Sentence Completion (R3)", "Illustrate/Support/Undermine (R3)", "Pair Passage (R4)"];
    const difficulties = ["Easy", "Medium", "Hard"];
    const passageLengths = ["under 50 words", "50 to 75 words", "75 to 100 words", "100 to 150 words", "over 150 words"];
    const bulletPointsLength = ["4 bullet points", "5 bullet points", "6 bullet points", "7 bullet points", "8 bullet points"];

    $: isStudentNotes = questionType === "Student's Notes (W1)";

    let questionType;
    let difficulty;
    let passageLength;

    let response = "";
    let isGenerating = false; 

    async function generateQuestion() {
        if (!questionType || !difficulty || !passageLength) {
            return;
        }

        isGenerating = true;

        const res = await fetch("/api/question-maker", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({ questionType, difficulty, passageLength }),
        });

        const data = await res.json();
        response = data.response;
        
        isGenerating = false;
    }
</script>

<div class="question-maker-title">
    Question Maker
</div>

<div class="question-maker-form">
    <div class="question-maker-options">
        <Dropdown label="Question Type" isRequired={true} bind:dropdownText={questionType} dropdownChoices={questionTypes}/>
        <Dropdown label="Difficulty" isRequired={true} bind:dropdownText={difficulty} dropdownChoices={difficulties}/>
        <Dropdown label="Passage Length" isRequired={true} bind:dropdownText={passageLength} dropdownChoices={isStudentNotes ? bulletPointsLength : passageLengths}/>    
    </div>
    <div>
        <Button onClick={generateQuestion}><span>Generate</span></Button>
    </div>
</div>

<div class="response">
    <H4>Question</H4>
    <pre class="converter-output"><code>{isGenerating ? "Generating..." : response !== "" ? response : "Output here."}</code></pre>
    <!-- <H4>Fix Something?</H4> -->
    <!-- <div class="button-row">
        <Button isSecondary={true}>Passage</Button>
        <Button isSecondary={true}>Choice A</Button>
        <Button isSecondary={true}>Choice B</Button>
        <Button isSecondary={true}>Choice C</Button>
        <Button isSecondary={true}>Choice D</Button>
        <Button isSecondary={true}>Explanation</Button>
    </div> -->
</div>

<style>
    .question-maker-title {
        font-family: "Inter";
        font-weight: 600;
        font-size: 36px;
    }

    .question-maker-form {
        display: flex;
        flex-direction: column;
        gap: 25px;
    }

    .question-maker-options {
        display: flex;
        flex-direction: column;
        gap: 6px;
    }

    .response {
        display: flex;
        flex-direction: column;
        gap: 0.75em;
    }

    .converter-output {
        display: flex;
        width: 100%;
        min-height: 18em;
        padding: 4px 12px;
        background-color: #F7F8F8;
        border: 1px solid #859DA3;
        border-radius: 4px;

        white-space: pre-wrap;       /* Since CSS 2.1 */
        white-space: -moz-pre-wrap;  /* Mozilla, since 1999 */
        white-space: -pre-wrap;      /* Opera 4-6 */
        white-space: -o-pre-wrap;    /* Opera 7 */
        word-wrap: break-word;  

        font-size: 1.125em;
        line-height: 1.4em;
    }

    /* .button-row {
        display: inline-flex;
        gap: 1em;
    } */
</style>