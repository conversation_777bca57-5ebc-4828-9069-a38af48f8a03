import { GoogleGenerativeA<PERSON> } from "@google/generative-ai";
import { AI_KEY } from '$env/static/private';

const genAI = new GoogleGenerativeAI(AI_KEY);

// Define the model here
// W1
const transitionModel = genAI.getGenerativeModel({
    model: "tunedModels/transition--dsat16-ywuye3gshz0v",
});

const studentsNotesModel = genAI.getGenerativeModel({
    model: "tunedModels/students-notes-31ve67vkpx2h",
});

// W2
const punctuationModel = genAI.getGenerativeModel({
    model: "tunedModels/punctuations--dsat16-deup01r2mv4d",
});

// W3
const grammarModel = genAI.getGenerativeModel({
    model: "tunedModels/grammar--dsat16-wk4gq7vj0oc7",
});

// R1
const wordInContextModel = genAI.getGenerativeModel({
    model: "tunedModels/word-in-context-v3-dsat16-c7uugvezsnrr",
});

// R2
const readingComprehensionModel = genAI.getGenerativeModel({
    model: "tunedModels/reading-comprehension--dsat16-yfgzlicpso",
});

// R3
const sentenceCompletionModel = genAI.getGenerativeModel({
    model: "tunedModels/sentence-completion--dsat16-dhbwvrsnrow1",
});

const illustrateModel = genAI.getGenerativeModel({
    model: "tunedModels/illustratesupportundermine-v2--dsat16-uw",
});

// R4
const pairPassageModel = genAI.getGenerativeModel({
    model: "tunedModels/pair-passage-ikgcbcruxvn9",
});

export const normalModel = genAI.getGenerativeModel({
  model: "gemini-1.5-flash",
});

export const models = {
    "Transitions (W1)": transitionModel,
    "Student's Notes (W1)": studentsNotesModel,
    "Punctuations (W2)": punctuationModel,
    "Grammar (W3)": grammarModel,
    "Word in Context (R1)": wordInContextModel,
    "Main Idea (R2)": readingComprehensionModel,
    "Specific Detail (R2)": readingComprehensionModel,
    "Main Purpose (R2)": readingComprehensionModel,
    "Overall Structure (R2)": readingComprehensionModel,
    "Sentence Completion (R3)": sentenceCompletionModel,
    "Illustrate/Support/Undermine (R3)": illustrateModel,
    "Pair Passage (R4)": pairPassageModel,
};

const generationConfig = {
    temperature: 0.9,
    topP: 0.8,
    topK: 64,
    maxOutputTokens: 2048,
    responseMimeType: "text/plain",
};

export async function run(model, prompt) {
    const chatSession = model.startChat({
        generationConfig,
    // safetySettings: Adjust safety settings
    // See https://ai.google.dev/gemini-api/docs/safety-settings
        history: [
        ],
    });

    const result = await chatSession.sendMessage(prompt);
    return result.response.text();
}


