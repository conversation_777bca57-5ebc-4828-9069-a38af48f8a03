<script>
	import WalkAnswerBox from "$lib/Simulation/WalkAnswerBox.svelte";

    const letters = ['A', 'B', 'C', 'D'];


    /**
     * @typedef {Object} Props
     * @property {any} data
     * @property {any} i
     * @property {any} isMath
     * @property {any} isSPR
     * @property {boolean} [isLeft]
     */

    /** @type {Props} */
    let {
        data,
        i,
        isMath,
        isSPR,
        isLeft = false
    } = $props();
</script>

<div class="right" class:right--left={isLeft}>
    <div class="mark-bar">
        <span class="number">{i + 1}</span>
        <button class="mark">
            <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 25 25" fill="none">
                    <path d="M16.6668 2.08301H8.3335C7.5047 2.08301 6.70985 2.41225 6.1238 2.9983C5.53774 3.58435 5.2085 4.37921 5.2085 5.20801V21.8747C5.20778 22.0582 5.25556 22.2387 5.34702 22.3979C5.43848 22.557 5.57037 22.6891 5.72934 22.7809C5.88769 22.8724 6.06732 22.9205 6.25017 22.9205C6.43302 22.9205 6.61265 22.8724 6.771 22.7809L12.5002 19.4684L18.2293 22.7809C18.3881 22.8709 18.5677 22.9176 18.7502 22.9163C18.9326 22.9176 19.1123 22.8709 19.271 22.7809C19.43 22.6891 19.5619 22.557 19.6533 22.3979C19.7448 22.2387 19.7926 22.0582 19.7918 21.8747V5.20801C19.7918 4.37921 19.4626 3.58435 18.8765 2.9983C18.2905 2.41225 17.4956 2.08301 16.6668 2.08301ZM17.7085 20.0726L13.021 17.3643C12.8627 17.2728 12.683 17.2247 12.5002 17.2247C12.3173 17.2247 12.1377 17.2728 11.9793 17.3643L7.29184 20.0726V5.20801C7.29184 4.93174 7.40158 4.66679 7.59694 4.47144C7.79229 4.27609 8.05724 4.16634 8.3335 4.16634H16.6668C16.9431 4.16634 17.2081 4.27609 17.4034 4.47144C17.5988 4.66679 17.7085 4.93174 17.7085 5.20801V20.0726Z" fill="black"/>
            </svg>
            <div class="mark-text">Mark for Review</div>
        </button>
    </div>

    <!-- Intro for Math -->
    {#key i}
        {#if isMath()}
            {#if data.practiceArena.questions[i].intro}
                <div class="intro-math">
                    {@html data.practiceArena.questions[i].intro}
                </div>
            {/if}

            <!-- Graph for Math -->
            {#if data.practiceArena.questions[i].graph}

                {#if Array.isArray(data.practiceArena.questions[i].graph)}
                    <div class={isSPR ?  "spr-table-wrapper" : "table-wrapper"}>
                        <table class={isSPR ?  "spr-table" : "table"}>
                            {#each data.practiceArena.questions[i].graph as row }
                                <tr>
                                    {#each row as item }
                                    <td>{@html item}</td>
                                    {/each}
                                </tr>
                            {/each}
                        </table>
                    </div>
                {:else}
                    <div class="graph-math">
                    <img src="{data.practiceArena.questions[i].graph}" alt={data.practiceArena.questions[i].graph}>
                    </div>
                {/if}

            {/if} 

        {/if}

        <div class="question">
            {@html data.practiceArena.questions[i].question}
        </div>

        {#if !isSPR}

            <div class="choices">
                {#each data.practiceArena.questions[i].choices as choice, index}
                    <div class="answer-container">
                        <button class="answer-choice" class:answer-correct={data.practiceArena.questions[i].correctAnswer === index}>
                            <div class="answer-letter-container">
                                <svg xmlns="http://www.w3.org/2000/svg" width="27" height="27" viewBox="0 0 27 27">
                                    <circle cx="13.5" cy="13.5" r="12.5" fill={data.practiceArena.questions[i].correctAnswer === index ? "#42FFB7" : "#FFFFFF"} stroke="#333333"/>
                                </svg>
                                <span class="answer-letter">{letters[index]}</span>
                            </div>

                            {#if Array.isArray(choice)}
                                <div class="choice-table-wrapper">
                                    <table class="table table--choice">
                                        {#each choice as row }
                                            <tr>
                                                {#each row as item }
                                                    <td>{@html item}</td>
                                                {/each}
                                            </tr>
                                        {/each}
                                    </table>
                                </div>
                            {:else}
                                <span class="answer-text">{@html choice}</span>
                            {/if}

                        </button>
                    </div>
                {/each}
            </div>

        {:else}
            <WalkAnswerBox correctAnswer={data.practiceArena.questions[i].correctAnswer}/>
        {/if}
    {/key}
    
</div>

<style>
    button {
        border: 0;
        background: none;
    }

    button:hover {
        opacity: 1 !important;
    }

    .right {
        display: flex;
        flex-direction: column;
        margin: 32px 64px 16px 16px;
        overflow-y: auto;
        scrollbar-color: #66E2FF#DAF8FF;
        scrollbar-width: thin;
    }

    .right > * {
        width: calc(100% - 6px);
        max-width: 651px;
    }

    .right--left {
        margin: 32px 16px 16px 32px;
        gap: 6px;
    }

    .mark-bar {
        display: inline-flex;
        height: 34px;
        flex-shrink: 0;
        background: #D9D9D9;
        align-items: center;
        border-bottom: 3px solid black;
        user-select: none;
    }

    .number {
        width: 29px;
        height: 34px;
        background: #000;
        flex-shrink: 0;
        color: #FFF;
        font-family: "Inter";
        font-size: 22px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .mark {
        display: inline-flex;
        align-items: center;
    }

    .mark svg {
        margin: 0 5px 0 12px;
    }

    .mark-text {
        color: #000;
        font-family: "Inter";
        font-size: 15px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
    }

    .question {
        color: var(--charcoal, #333);
        font-family: "Merriweather";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 25.1px;
        margin-top: 6px;
    }

    .answer-choice {
        display: inline-flex;
        min-height: 50px;
        width: 100%;
        max-width: 700px;
        padding: 12px 13px;
        align-items: center;
        gap: 21px;
        border-radius: 8px;
        border: 1px solid var(--charcoal, #333);
    }

    .answer-container {
        margin-top: 16px;
    }

    .answer-correct {
        background-color: var(--web-color-light-aquamarine, #D1FFEE);
    }

    .answer-letter-container {
        display: flex;
        align-items: center;
        position: relative;
        stroke-width: 2px;
        stroke: var(--charcoal, #333);
    }

    .answer-letter {
        position: absolute;
        left: 50%;
        transform: translate(-50%, 0);
        color: var(--charcoal, #333);
        font-family: "Inter";
        font-size: 15px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
    }

    .answer-text {
        color: var(--charcoal, #333);
        /* p1 Test */
        font-family: "Merriweather";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 25.1px; /* 156.875% */
        text-align: start;
    }   

    .spr-table-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .spr-table {
        width: 380px;
        border: 1px solid black;
        border-collapse: collapse;
        text-align: center;
    }

    .spr-table td {
        padding: 13px 10px;
        border: 1px solid black;
        border-collapse: collapse;
    }

    .spr-table tr {
        border: 1px solid black;
        border-collapse: collapse;
        /* p1 Test */
        font-family: "Merriweather";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 25.1px; /* 156.875% */
    }

    .graph-math {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .graph-math img {
        object-fit: contain;
        width: 380px;
    }   
    
    .table {
        width: 100%;
        text-align: center;
        border: 1px solid black;
        border-collapse: collapse;
    }

    .table tr {
        width: 50%;
        border: 1px solid black;
        border-collapse: collapse;
    }

    .table td {
        padding: 14px 10px;
        border: 1px solid black;
        border-collapse: collapse;
        color: var(--Charcoal, #333);

        /* p1 Test */
        font-family: "Merriweather";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 25.1px; /* 156.875% */
    }

    .choice-table-wrapper {
        margin: 12px 0;
    }

    .table--choice td {
        padding: 13px 22px;
    }
    
    .intro-math {
        color: var(--Charcoal, #333);
        /* p1 Test */
        font-family: "Merriweather";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 25.1px; /* 156.875% */
    }

    /* width */
    ::-webkit-scrollbar {
        width: 8px;
        margin: 4px 0;
    }

    /* Track */
    ::-webkit-scrollbar-track {
        background: #DAF8FF;
        border-radius: 5px;
    }

    /* Handle */
    ::-webkit-scrollbar-thumb {
        background: #66E2FF;
        border-radius: 5px;
    }
</style>