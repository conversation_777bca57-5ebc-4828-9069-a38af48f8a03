<script>
    /**
     * @typedef {Object} Props
     * @property {any} data
     * @property {any} setQuestion
     * @property {boolean} [isMinitest]
     * @property {import('svelte').Snippet} [children]
     */

    /** @type {Props} */
    let {
        data,
        setQuestion,
        isMinitest = false,
        children
    } = $props();
</script>

<div class="middle-container" class:--middle-full={isMinitest}>
    <div class="middle">
        <div class="check">This is a preview</div>
        <div class="on">There are <span class="bold">{data.practiceArena.questions.length}</span> questions</div>
        <div class="for">You can double check them by clicking each question below.</div>
        <div class="review-nav">
            <div class="nav-answer-list">
                {#each data.practiceArena.questions as question, index}
                    <div class="answer-box-container">
                        <button class="answer-box" onclick={() => setQuestion(index)}>{index + 1}</button>    
                    </div>
                {/each}
            </div>
        </div>
        {@render children?.()}
    </div>
</div>

<style>
    button {
        border: 0;
        background: none;
    }

    button:hover {
        opacity: 1 !important;
    }

    .middle-container {
        display: flex;
        align-items: center;
        width: 100%;
        height: calc(100vh - 180px);
        flex-direction: column;
        overflow: auto;
        scrollbar-color: #66E2FF#DAF8FF;
        scrollbar-width: thin;
    }

    .middle:last-child {
        margin-bottom: 64px;
    }

    .--middle-full {
        height: 100%;
    }

    /* width */
    ::-webkit-scrollbar {
        width: 8px;
        margin: 4px 0;
    }

    /* Track */
    ::-webkit-scrollbar-track {
        background: #DAF8FF;
        border-radius: 5px;
    }

    /* Handle */
    ::-webkit-scrollbar-thumb {
        background: #66E2FF;
        border-radius: 5px;
    }

    .middle {
        min-width: 850px;
        max-width: 1200px;
        width: 70%;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .check {
        margin-top: 55px;
        color: #000;
        text-align: center;
        font-family: "Inter";
        font-size: 32px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
    }

    .on {
        margin-top: 33px;
        color: #000;
        text-align: center;
        font-family: "Inter";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
    }

    .for {
        margin-top: 13px;
        color: #000;
        text-align: center;
        font-family: "Inter";
        font-size: 16px;
        font-style: normal;
        font-weight: 800;
        line-height: normal;
        text-decoration: underline;
    }

    .review-nav {
        margin: 30px 0;
        display: inline-flex;
        padding: 35px;
        flex-direction: column;
        gap: 40px;
        align-items: center;
        border-radius: 8px;
        background: #FFF;
        box-shadow: 0px 0px 7px 0px rgba(0, 0, 0, 0.25);
    }

    span.bold {
        font-weight: 700;
    }

    .nav-answer-list {
        display: flex;
        align-items: flex-end;
        align-content: flex-end;
        gap: 25px 35px;
        flex-wrap: wrap;
    }

    .answer-box-container {
        position: relative;
    }

    .answer-box {
        width: 45px;
        height: 45px;
        flex-shrink: 0;
        color: #000;
        text-align: center;
        font-family: "Inter";
        font-size: 25px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
        border: 1px dashed #000;
    }
</style>