import { previewClient } from '$lib/server/contentful.js';
import { error } from '@sveltejs/kit';
import { documentToHtmlString } from '@contentful/rich-text-html-renderer';
import { BLOCKS } from '@contentful/rich-text-types';

export async function load({ url }) {
    // Fetch the practice arena
    const type = url.searchParams.get('type'); 
    const id = url.searchParams.get('id');

    // Fetch the practice arena
    let practiceArena = await previewClient.getEntry(id).catch((e) => error(404, e.message));
    practiceArena = practiceArena.fields;

    if (!practiceArena) error(404);    

    if (type === 'question') {
        practiceArena = {
            title: 'Question',
            questions: [practiceArena],
        };
    } else if (type === 'arena') {
        // Extract the data
        practiceArena.questions = practiceArena.questions.map(question => question.fields);
    }

    practiceArena.questions.forEach(question => {
        question.graph = question.graph?.fields.file.url;
        question.passage = question.passage?.passage;
        question.correctAnswer = question.correctAnswer.correctAnswer;

        const options = {
            renderNode: {
              [BLOCKS.UL_LIST]: (node, next) => `<ul style="padding-left: 32px; display: flex; flex-direction: column; gap: 4px;">${next(node.content)}</ul>`,
              [BLOCKS.EMBEDDED_ASSET]: ({ data: { target: { fields }}}) =>
                `<div style="display: flex; justify-content: center; width: 100%"><img src="${fields.file.url}" width="70%" alt="${fields.title}"/></div>`,
            }
          }

        question.explanation = documentToHtmlString(question.explanation, options);

        if (question.choiceA) {
            question.choices = [
                question.choiceA,
                question.choiceB,
                question.choiceC,
                question.choiceD
            ]
        } else {
            question.choices = null;
        }

        delete question.choiceA, question.choiceB, question.choiceC, question.choiceD;
    });    
    
    return { 
        practiceArena: practiceArena,
        type: type,
    };
}
