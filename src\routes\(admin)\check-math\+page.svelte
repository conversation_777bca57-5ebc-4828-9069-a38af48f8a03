<script>
	import { onMount } from 'svelte';
	import testData from '$lib/test.json';
	import katex from 'katex';

	let mathRendered = false;

	onMount(() => {
		// Import KaTeX CSS
		const link = document.createElement('link');
		link.rel = 'stylesheet';
		link.href = 'https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.css';
		document.head.appendChild(link);

		// Render all LaTeX expressions after component mounts
		renderMath();
		mathRendered = true;
	});

	function renderMath() {
		// Find all elements with LaTeX content and render them
		const elements = document.querySelectorAll('.math-content');
		elements.forEach(element => {
			const text = element.textContent;
			if (text && (text.includes('$') || text.includes('\\('))) {
				try {
					// First, temporarily replace escaped dollar signs to protect them
					const ESCAPED_DOLLAR_PLACEHOLDER = '___ESCAPED_DOLLAR___';
					let html = text.replace(/\\\$/g, ESCAPED_DOLLAR_PLACEHOLDER);

					// Replace math delimiters in the correct order (display math first to avoid conflicts)
					html = html
						// Handle display math ($$...$$) first - these create block-level math with newlines
						.replace(/\$\$([^$]+?)\$\$/g, (_, math) => {
							return katex.renderToString(math.trim(), { displayMode: true });
						})
						// Handle inline math ($...$)
						.replace(/\$([^$]+?)\$/g, (_, math) => {
							return katex.renderToString(math.trim(), { displayMode: false });
						})
						// Handle LaTeX parentheses notation \(...\)
						.replace(/\\\(([^)]+?)\\\)/g, (_, math) => {
							return katex.renderToString(math.trim(), { displayMode: false });
						});

					// Restore escaped dollar signs as literal dollar signs
					html = html.replace(new RegExp(ESCAPED_DOLLAR_PLACEHOLDER, 'g'), '$');

					element.innerHTML = html;
				} catch (error) {
					console.warn('KaTeX rendering error:', error);
					// Keep original text if rendering fails
				}
			}
		});
	}

	// Re-render math when data changes
	$: if (mathRendered && testData) {
		setTimeout(renderMath, 0);
	}
</script>

<svelte:head>
	<title>Check Math - DSAT16 Admin</title>
</svelte:head>

<div class="container">
	<h1>Mathematical Content Test Page</h1>
	<p class="description">
		This page displays mathematical content from the test.json file with LaTeX rendering using KaTeX.
	</p>

	<div class="questions">
		{#each testData as question}
			<div class="question-card">
				<div class="question-header">
					<h2>Question {question.question_number}</h2>
					<div class="question-type">
						{question.is_text_only ? 'Text Only' : 'Multiple Choice'}
					</div>
				</div>

				{#if question.passage}
					<div class="passage-section">
						<h3>Passage:</h3>
						<div class="math-content passage-content">
							{question.passage}
						</div>
					</div>
				{/if}

				<div class="question-section">
					<h3>Question:</h3>
					<div class="math-content question-content">
						{question.question}
					</div>
				</div>

				{#if question.answer_choices}
					<div class="choices-section">
						<h3>Answer Choices:</h3>
						<div class="choices-list">
							{#each question.answer_choices as choice, choiceIndex}
								<div class="choice-item">
									<span class="choice-letter">
										{String.fromCharCode(65 + choiceIndex)}:
									</span>
									<div class="math-content choice-content">
										{choice}
									</div>
								</div>
							{/each}
						</div>
					</div>
				{/if}
			</div>
		{/each}
	</div>
</div>

<style>
	.container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 2rem;
		font-family: 'Open Sans', sans-serif;
	}

	h1 {
		color: #333;
		margin-bottom: 1rem;
		font-size: 2.5rem;
	}

	.description {
		color: #666;
		margin-bottom: 2rem;
		font-size: 1.1rem;
		line-height: 1.6;
	}

	.questions {
		display: flex;
		flex-direction: column;
		gap: 2rem;
	}

	.question-card {
		border: 1px solid #ddd;
		border-radius: 12px;
		padding: 1.5rem;
		background: white;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	}

	.question-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 1.5rem;
		padding-bottom: 1rem;
		border-bottom: 2px solid #f0f0f0;
	}

	.question-header h2 {
		color: #333;
		margin: 0;
		font-size: 1.5rem;
	}

	.question-type {
		background: #e3f2fd;
		color: #1976d2;
		padding: 0.5rem 1rem;
		border-radius: 20px;
		font-size: 0.9rem;
		font-weight: 600;
	}

	.passage-section,
	.question-section,
	.choices-section {
		margin-bottom: 1.5rem;
	}

	.passage-section h3,
	.question-section h3,
	.choices-section h3 {
		color: #555;
		margin-bottom: 0.75rem;
		font-size: 1.2rem;
		font-weight: 600;
	}

	.math-content {
		line-height: 1.8;
		font-size: 1rem;
	}

	.passage-content {
		background: #f8f9fa;
		padding: 1rem;
		border-radius: 8px;
		border-left: 4px solid #28a745;
	}

	.question-content {
		background: #fff3cd;
		padding: 1rem;
		border-radius: 8px;
		border-left: 4px solid #ffc107;
		font-weight: 500;
	}

	.choices-list {
		display: flex;
		flex-direction: column;
		gap: 0.75rem;
	}

	.choice-item {
		display: flex;
		align-items: flex-start;
		gap: 0.75rem;
		padding: 0.75rem;
		background: #f8f9fa;
		border-radius: 8px;
		border: 1px solid #e9ecef;
	}

	.choice-letter {
		font-weight: 700;
		color: #495057;
		min-width: 1.5rem;
		margin-top: 0.1rem;
	}

	.choice-content {
		flex: 1;
	}

	/* Responsive design */
	@media (max-width: 768px) {
		.container {
			padding: 1rem;
		}

		h1 {
			font-size: 2rem;
		}

		.question-header {
			flex-direction: column;
			align-items: flex-start;
			gap: 1rem;
		}

		.choice-item {
			flex-direction: column;
			gap: 0.5rem;
		}

		.choice-letter {
			min-width: auto;
		}
	}

	/* KaTeX styling adjustments */
	:global(.katex) {
		font-size: 1.1em;
	}

	:global(.katex-display) {
		margin: 1rem 0;
	}
</style>
