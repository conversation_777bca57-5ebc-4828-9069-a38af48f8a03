<script>
	import { onMount } from 'svelte';
	import katex from 'katex';

	let mathRendered = false;
	let testData = [];
	let fileInput;
	let fileName = '';
	let isLoading = false;
	let error = '';

	onMount(() => {
		// Import KaTeX CSS
		const link = document.createElement('link');
		link.rel = 'stylesheet';
		link.href = 'https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.css';
		document.head.appendChild(link);

		mathRendered = true;
	});

	function handleFileUpload(event) {
		const file = event.target.files[0];
		if (!file) return;

		if (!file.name.endsWith('.json')) {
			error = 'Please select a JSON file';
			return;
		}

		fileName = file.name;
		isLoading = true;
		error = '';

		const reader = new FileReader();
		reader.onload = (e) => {
			try {
				const jsonData = JSON.parse(e.target.result);
				if (Array.isArray(jsonData)) {
					testData = jsonData;
					error = '';
					// Render math after data is loaded
					setTimeout(renderMath, 0);
				} else {
					error = 'JSON file must contain an array of questions';
					testData = [];
				}
			} catch (err) {
				error = 'Invalid JSON file format';
				testData = [];
			} finally {
				isLoading = false;
			}
		};

		reader.onerror = () => {
			error = 'Error reading file';
			isLoading = false;
		};

		reader.readAsText(file);
	}

	function clearFile() {
		testData = [];
		fileName = '';
		error = '';
		if (fileInput) {
			fileInput.value = '';
		}
	}

	function renderMath() {
		// Find all elements with LaTeX content and render them
		const elements = document.querySelectorAll('.math-content');
		elements.forEach(element => {
			const text = element.textContent;
			if (text && (text.includes('$') || text.includes('\\('))) {
				try {
					// First, temporarily replace escaped dollar signs to protect them
					const ESCAPED_DOLLAR_PLACEHOLDER = '___ESCAPED_DOLLAR___';
					let html = text.replace(/\\\$/g, ESCAPED_DOLLAR_PLACEHOLDER);

					// Replace math delimiters in the correct order (display math first to avoid conflicts)
					html = html
						// Handle display math ($$...$$) first - these create block-level math with newlines
						.replace(/\$\$([^$]+?)\$\$/g, (_, math) => {
							return katex.renderToString(math.trim(), { displayMode: true });
						})
						// Handle inline math ($...$)
						.replace(/\$([^$]+?)\$/g, (_, math) => {
							return katex.renderToString(math.trim(), { displayMode: false });
						})
						// Handle LaTeX parentheses notation \(...\)
						.replace(/\\\(([^)]+?)\\\)/g, (_, math) => {
							return katex.renderToString(math.trim(), { displayMode: false });
						});

					// Restore escaped dollar signs as literal dollar signs
					html = html.replace(new RegExp(ESCAPED_DOLLAR_PLACEHOLDER, 'g'), '$');

					element.innerHTML = html;
				} catch (error) {
					console.warn('KaTeX rendering error:', error);
					// Keep original text if rendering fails
				}
			}
		});
	}

	// Re-render math when data changes
	$: if (mathRendered && testData) {
		setTimeout(renderMath, 0);
	}
</script>

<svelte:head>
	<title>Check Math - DSAT16 Admin</title>
</svelte:head>

<div class="container">
	<h1>Mathematical Content Test Page</h1>
	<p class="description">
		Upload a JSON file containing mathematical questions to display them with LaTeX rendering using KaTeX.
	</p>

	<!-- File Upload Section -->
	<div class="upload-section">
		<div class="upload-controls">
			<input
				type="file"
				accept=".json"
				bind:this={fileInput}
				on:change={handleFileUpload}
				class="file-input"
				id="json-file"
			/>
			<label for="json-file" class="file-label">
				<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
					<path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
					<polyline points="14,2 14,8 20,8"/>
					<line x1="16" y1="13" x2="8" y2="13"/>
					<line x1="16" y1="17" x2="8" y2="17"/>
					<line x1="10" y1="9" x2="8" y2="9"/>
				</svg>
				Choose JSON File
			</label>

			{#if fileName}
				<div class="file-info">
					<span class="file-name">{fileName}</span>
					<button class="clear-btn" on:click={clearFile} aria-label="Clear selected file">
						<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
							<line x1="18" y1="6" x2="6" y2="18"/>
							<line x1="6" y1="6" x2="18" y2="18"/>
						</svg>
					</button>
				</div>
			{/if}
		</div>

		{#if isLoading}
			<div class="loading">Loading and processing file...</div>
		{/if}

		{#if error}
			<div class="error">{error}</div>
		{/if}
	</div>

	<!-- Questions Display -->
	{#if testData.length > 0}
		<div class="questions">
		{#each testData as question}
			<div class="question-card">
				<div class="question-header">
					<h2>Question {question.question_number}</h2>
					<div class="question-type">
						{question.is_text_only ? 'Text Only' : 'Multiple Choice'}
					</div>
				</div>

				{#if question.passage}
					<div class="passage-section">
						<h3>Passage:</h3>
						<div class="math-content passage-content">
							{question.passage}
						</div>
					</div>
				{/if}

				<div class="question-section">
					<h3>Question:</h3>
					<div class="math-content question-content">
						{question.question}
					</div>
				</div>

				{#if question.answer_choices}
					<div class="choices-section">
						<h3>Answer Choices:</h3>
						<div class="choices-list">
							{#each question.answer_choices as choice, choiceIndex}
								<div class="choice-item">
									<span class="choice-letter">
										{String.fromCharCode(65 + choiceIndex)}:
									</span>
									<div class="math-content choice-content">
										{choice}
									</div>
								</div>
							{/each}
						</div>
					</div>
				{/if}
			</div>
		{/each}
	</div>
	{:else}
		<div class="no-data">
			<p>No data to display. Please upload a JSON file to get started.</p>
		</div>
	{/if}
</div>

<style>
	.container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 2rem;
		font-family: 'Open Sans', sans-serif;
	}

	h1 {
		color: #333;
		margin-bottom: 1rem;
		font-size: 2.5rem;
	}

	.description {
		color: #666;
		margin-bottom: 2rem;
		font-size: 1.1rem;
		line-height: 1.6;
	}

	.upload-section {
		background: #f8f9fa;
		border: 2px dashed #dee2e6;
		border-radius: 12px;
		padding: 2rem;
		margin-bottom: 2rem;
		text-align: center;
	}

	.upload-controls {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 1rem;
	}

	.file-input {
		display: none;
	}

	.file-label {
		display: inline-flex;
		align-items: center;
		gap: 0.5rem;
		padding: 0.75rem 1.5rem;
		background: #007bff;
		color: white;
		border-radius: 8px;
		cursor: pointer;
		font-weight: 600;
		transition: background-color 0.2s;
	}

	.file-label:hover {
		background: #0056b3;
	}

	.file-info {
		display: flex;
		align-items: center;
		gap: 0.75rem;
		padding: 0.5rem 1rem;
		background: white;
		border: 1px solid #dee2e6;
		border-radius: 8px;
	}

	.file-name {
		font-weight: 500;
		color: #495057;
	}

	.clear-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 24px;
		height: 24px;
		background: #dc3545;
		color: white;
		border-radius: 50%;
		cursor: pointer;
		transition: background-color 0.2s;
	}

	.clear-btn:hover {
		background: #c82333;
	}

	.loading {
		color: #007bff;
		font-weight: 500;
		margin-top: 1rem;
	}

	.error {
		color: #dc3545;
		font-weight: 500;
		margin-top: 1rem;
		padding: 0.75rem;
		background: #f8d7da;
		border: 1px solid #f5c6cb;
		border-radius: 8px;
	}

	.no-data {
		text-align: center;
		padding: 3rem;
		color: #6c757d;
		font-size: 1.1rem;
	}

	.questions {
		display: flex;
		flex-direction: column;
		gap: 2rem;
	}

	.question-card {
		border: 1px solid #ddd;
		border-radius: 12px;
		padding: 1.5rem;
		background: white;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	}

	.question-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 1.5rem;
		padding-bottom: 1rem;
		border-bottom: 2px solid #f0f0f0;
	}

	.question-header h2 {
		color: #333;
		margin: 0;
		font-size: 1.5rem;
	}

	.question-type {
		background: #e3f2fd;
		color: #1976d2;
		padding: 0.5rem 1rem;
		border-radius: 20px;
		font-size: 0.9rem;
		font-weight: 600;
	}

	.passage-section,
	.question-section,
	.choices-section {
		margin-bottom: 1.5rem;
	}

	.passage-section h3,
	.question-section h3,
	.choices-section h3 {
		color: #555;
		margin-bottom: 0.75rem;
		font-size: 1.2rem;
		font-weight: 600;
	}

	.math-content {
		line-height: 1.8;
		font-size: 1rem;
	}

	.passage-content {
		background: #f8f9fa;
		padding: 1rem;
		border-radius: 8px;
		border-left: 4px solid #28a745;
	}

	.question-content {
		background: #fff3cd;
		padding: 1rem;
		border-radius: 8px;
		border-left: 4px solid #ffc107;
		font-weight: 500;
	}

	.choices-list {
		display: flex;
		flex-direction: column;
		gap: 0.75rem;
	}

	.choice-item {
		display: flex;
		align-items: flex-start;
		gap: 0.75rem;
		padding: 0.75rem;
		background: #f8f9fa;
		border-radius: 8px;
		border: 1px solid #e9ecef;
	}

	.choice-letter {
		font-weight: 700;
		color: #495057;
		min-width: 1.5rem;
		margin-top: 0.1rem;
	}

	.choice-content {
		flex: 1;
	}

	/* Responsive design */
	@media (max-width: 768px) {
		.container {
			padding: 1rem;
		}

		h1 {
			font-size: 2rem;
		}

		.question-header {
			flex-direction: column;
			align-items: flex-start;
			gap: 1rem;
		}

		.choice-item {
			flex-direction: column;
			gap: 0.5rem;
		}

		.choice-letter {
			min-width: auto;
		}
	}

	/* KaTeX styling adjustments */
	:global(.katex) {
		font-size: 1.1em;
	}

	:global(.katex-display) {
		margin: 1rem 0;
	}
</style>
