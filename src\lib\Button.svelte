<script>
    /**
     * @typedef {Object} Props
     * @property {any} [onClick]
     * @property {boolean} [isSecondary]
     * @property {import('svelte').Snippet} [children]
     */

    /** @type {Props} */
    let { onClick = () => {}, isSecondary = false, children } = $props();
</script>

<button class:primary={isSecondary} onclick={onClick}>
    {@render children?.()}
</button>

<style>
    button {
        padding: 12px 24px;
        border: 1px solid #000000;
        border-radius: 8px;
        box-shadow: 4px 4px;
        background-color: #66E2FF;

        display: inline-flex;
        gap: 10px;

        font-family: "Open Sans";
        font-weight: 600;
        font-size: 18px;
        color: #000000;
    }

    .primary {
        background-color: #FFFFFF;
    }

    button:active {
        box-shadow: none;
        transform: translate(4px, 4px);
    }
</style>